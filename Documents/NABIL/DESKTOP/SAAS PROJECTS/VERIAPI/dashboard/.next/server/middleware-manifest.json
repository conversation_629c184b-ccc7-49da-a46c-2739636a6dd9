{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "b5+gSLmqkCMyF8NnHhWX2fNldyjB1dMkNre6pfk6TWc="}}}, "functions": {}, "sortedMiddleware": ["/"]}