/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/login/page";
exports.ids = ["app/login/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(rsc)/./src/app/login/page.tsx\")), \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/login/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fsrc%2Fhooks%2FuseAuth.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fsrc%2Fhooks%2FuseAuth.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/hooks/useAuth.tsx */ \"(ssr)/./src/hooks/useAuth.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fsrc%2Fhooks%2FuseAuth.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fsrc%2Fapp%2Flogin%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fsrc%2Fapp%2Flogin%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(ssr)/./src/app/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbWFjbmFiaWwlMkZEb2N1bWVudHMlMkZOQUJJTCUyRkRFU0tUT1AlMkZTQUFTJTIwUFJPSkVDVFMlMkZWRVJJQVBJJTJGZGFzaGJvYXJkJTJGc3JjJTJGYXBwJTJGbG9naW4lMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNEpBQW1JIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdmVyaWFwaS1kYXNoYm9hcmQvPzY1YzYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvbWFjbmFiaWwvRG9jdW1lbnRzL05BQklML0RFU0tUT1AvU0FBUyBQUk9KRUNUUy9WRVJJQVBJL2Rhc2hib2FyZC9zcmMvYXBwL2xvZ2luL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fsrc%2Fapp%2Flogin%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hook-form */ \"(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(ssr)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(ssr)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,LogIn,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,LogIn,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,LogIn,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,LogIn,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,LogIn,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAuth */ \"(ssr)/./src/hooks/useAuth.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst loginSchema = zod__WEBPACK_IMPORTED_MODULE_5__.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_5__.string().email(\"Invalid email address\"),\n    password: zod__WEBPACK_IMPORTED_MODULE_5__.string().min(1, \"Password is required\")\n});\nfunction LoginPage() {\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { login, isAuthenticated } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { register, handleSubmit, formState: { errors } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(loginSchema)\n    });\n    // Redirect if already authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isAuthenticated) {\n            router.push(\"/dashboard\");\n        }\n    }, [\n        isAuthenticated,\n        router\n    ]);\n    const onSubmit = async (data)=>{\n        try {\n            setIsLoading(true);\n            await login(data);\n        } catch (error) {\n        // Error is handled by the auth context\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-6 w-6 text-white\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-6 text-3xl font-extrabold text-gray-900\",\n                            children: \"\\xa1Bienvenido de vuelta!\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-sm text-gray-600\",\n                            children: \"Inicia sesi\\xf3n en tu cuenta\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white py-8 px-6 shadow-lg rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            className: \"space-y-6\",\n                            onSubmit: handleSubmit(onSubmit),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                                        lineNumber: 74,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    ...register(\"email\"),\n                                                    type: \"email\",\n                                                    autoComplete: \"email\",\n                                                    className: `input pl-10 ${errors.email ? \"border-red-300 focus:ring-red-500 focus:border-red-500\" : \"\"}`,\n                                                    placeholder: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-600\",\n                                            children: errors.email.message\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Contrase\\xf1a\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    ...register(\"password\"),\n                                                    type: showPassword ? \"text\" : \"password\",\n                                                    autoComplete: \"current-password\",\n                                                    className: `input pl-10 pr-10 ${errors.password ? \"border-red-300 focus:ring-red-500 focus:border-red-500\" : \"\"}`,\n                                                    placeholder: \"••••••••\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400 hover:text-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400 hover:text-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-600\",\n                                            children: errors.password.message\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isLoading,\n                                        className: \"btn btn-primary btn-md w-full flex items-center justify-center\",\n                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"spinner mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Cargando...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Iniciar Sesi\\xf3n\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full border-t border-gray-300\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex justify-center text-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 bg-white text-gray-500\",\n                                            children: \"VeriAPI Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500\",\n                                children: \"Sistema de Facturaci\\xf3n Electr\\xf3nica\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-yellow-800 mb-2\",\n                            children: \"Credenciales de Prueba\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-yellow-700 space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Email:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 16\n                                        }, this),\n                                        \" <EMAIL>\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Password:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 16\n                                        }, this),\n                                        \" 123456\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-600 mt-2\",\n                                    children: \"Usa estas credenciales para probar el sistema\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: async ()=>{\n                                try {\n                                    const response = await fetch(\"http://localhost:3000/health\");\n                                    const data = await response.json();\n                                    console.log(\"Backend health check:\", data);\n                                    alert(\"Backend conectado: \" + data.message);\n                                } catch (error) {\n                                    console.error(\"Backend connection error:\", error);\n                                    alert(\"Error conectando al backend: \" + error.message);\n                                }\n                            },\n                            className: \"mt-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\",\n                            children: \"Probar Conexi\\xf3n Backend\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAuth.tsx":
/*!*******************************!*\
  !*** ./src/hooks/useAuth.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useRequireAuth: () => (/* binding */ useRequireAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,useRequireAuth auto */ \n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Check if user is authenticated on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkAuth = async ()=>{\n            try {\n                if (_lib_api__WEBPACK_IMPORTED_MODULE_4__.authApi.isAuthenticated()) {\n                    // If we have a token, we could fetch user data here\n                    // For now, we'll just set loading to false\n                    // In a real app, you might want to validate the token with the server\n                    setLoading(false);\n                } else {\n                    setLoading(false);\n                }\n            } catch (error) {\n                console.error(\"Auth check failed:\", error);\n                setLoading(false);\n            }\n        };\n        checkAuth();\n    }, []);\n    const login = async (credentials)=>{\n        try {\n            setLoading(true);\n            console.log(\"Attempting login with:\", credentials);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.authApi.login(credentials);\n            console.log(\"Login response:\", response);\n            setUser(response.user);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"\\xa1Bienvenido de vuelta!\");\n            router.push(\"/dashboard\");\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            const message = error.response?.data?.message || error.response?.data?.error || error.message || \"Error al iniciar sesi\\xf3n\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error(message);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const register = async (data)=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.authApi.register(data);\n            setUser(response.user);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"\\xa1Cuenta creada exitosamente!\");\n            router.push(\"/dashboard\");\n        } catch (error) {\n            const message = error.response?.data?.error || \"Error al crear la cuenta\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error(message);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const logout = ()=>{\n        setUser(null);\n        _lib_api__WEBPACK_IMPORTED_MODULE_4__.authApi.logout();\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Sesi\\xf3n cerrada correctamente\");\n    };\n    const value = {\n        user,\n        loading,\n        login,\n        register,\n        logout,\n        isAuthenticated: !!user || _lib_api__WEBPACK_IMPORTED_MODULE_4__.authApi.isAuthenticated()\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/hooks/useAuth.tsx\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n// Hook for protecting routes\nfunction useRequireAuth() {\n    const { isAuthenticated, loading } = useAuth();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && !isAuthenticated) {\n            router.push(\"/login\");\n        }\n    }, [\n        isAuthenticated,\n        loading,\n        router\n    ]);\n    return {\n        isAuthenticated,\n        loading\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAuth.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   companyApi: () => (/* binding */ companyApi),\n/* harmony export */   customersApi: () => (/* binding */ customersApi),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   invoicesApi: () => (/* binding */ invoicesApi)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n// API Configuration\nconst API_BASE_URL = \"http://localhost:3000/api\" || 0;\nconst TOKEN_KEY = \"veriapi_token\";\n// Create axios instance\nconst createApiClient = ()=>{\n    const client = axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].create({\n        baseURL: API_BASE_URL,\n        timeout: 30000,\n        headers: {\n            \"Content-Type\": \"application/json\"\n        }\n    });\n    // Request interceptor to add auth token\n    client.interceptors.request.use((config)=>{\n        console.log(\"API Request:\", config.method?.toUpperCase(), config.url, config.data);\n        const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(TOKEN_KEY);\n        if (token) {\n            config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n    }, (error)=>{\n        console.error(\"Request interceptor error:\", error);\n        return Promise.reject(error);\n    });\n    // Response interceptor for error handling\n    client.interceptors.response.use((response)=>{\n        console.log(\"API Response:\", response.status, response.config.url, response.data);\n        return response;\n    }, (error)=>{\n        console.error(\"API Error:\", error.response?.status, error.response?.data, error.message);\n        const message = error.response?.data?.message || error.response?.data?.error || error.message || \"An error occurred\";\n        if (error.response?.status === 401) {\n            // Unauthorized - clear token and redirect to login\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(TOKEN_KEY);\n            if (false) {}\n        } else if (error.response?.status >= 500) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.toast.error(\"Server error. Please try again later.\");\n        } else {\n            // Don't show toast here, let the calling component handle it\n            console.log(\"API error message:\", message);\n        }\n        return Promise.reject(error);\n    });\n    return client;\n};\nconst api = createApiClient();\n// Auth API\nconst authApi = {\n    login: async (credentials)=>{\n        const response = await api.post(\"/auth/login\", credentials);\n        // Store token in cookie\n        if (response.data.token) {\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(TOKEN_KEY, response.data.token, {\n                expires: 7,\n                secure: \"development\" === \"production\",\n                sameSite: \"strict\"\n            });\n        }\n        return response.data;\n    },\n    register: async (data)=>{\n        const response = await api.post(\"/auth/register\", data);\n        // Store token in cookie\n        if (response.data.token) {\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(TOKEN_KEY, response.data.token, {\n                expires: 7,\n                secure: \"development\" === \"production\",\n                sameSite: \"strict\"\n            });\n        }\n        return response.data;\n    },\n    logout: ()=>{\n        js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(TOKEN_KEY);\n        if (false) {}\n    },\n    getToken: ()=>{\n        return js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(TOKEN_KEY);\n    },\n    isAuthenticated: ()=>{\n        return !!js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(TOKEN_KEY);\n    }\n};\n// Company API\nconst companyApi = {\n    getCompany: async ()=>{\n        const response = await api.get(\"/company\");\n        return response.data.company;\n    },\n    updateCompany: async (data)=>{\n        const response = await api.put(\"/company\", data);\n        return response.data.company;\n    },\n    getStats: async ()=>{\n        const response = await api.get(\"/company/stats\");\n        return response.data.stats;\n    },\n    getNextInvoiceNumber: async ()=>{\n        const response = await api.get(\"/company/next-invoice-number\");\n        return response.data;\n    }\n};\n// Customers API\nconst customersApi = {\n    getCustomers: async (filters)=>{\n        const params = new URLSearchParams();\n        if (filters?.page) params.append(\"page\", filters.page.toString());\n        if (filters?.limit) params.append(\"limit\", filters.limit.toString());\n        if (filters?.search) params.append(\"search\", filters.search);\n        if (filters?.customerType) params.append(\"customerType\", filters.customerType);\n        if (filters?.isActive !== undefined) params.append(\"isActive\", filters.isActive.toString());\n        const response = await api.get(`/customers?${params.toString()}`);\n        return response.data;\n    },\n    getCustomer: async (id)=>{\n        const response = await api.get(`/customers/${id}`);\n        return response.data.customer;\n    },\n    createCustomer: async (data)=>{\n        const response = await api.post(\"/customers\", data);\n        return response.data.customer;\n    },\n    updateCustomer: async (id, data)=>{\n        const response = await api.put(`/customers/${id}`, data);\n        return response.data.customer;\n    },\n    deleteCustomer: async (id)=>{\n        await api.delete(`/customers/${id}`);\n    }\n};\n// Invoices API\nconst invoicesApi = {\n    getInvoices: async (filters)=>{\n        const params = new URLSearchParams();\n        if (filters?.page) params.append(\"page\", filters.page.toString());\n        if (filters?.limit) params.append(\"limit\", filters.limit.toString());\n        if (filters?.status) params.append(\"status\", filters.status);\n        if (filters?.customer) params.append(\"customer\", filters.customer);\n        if (filters?.dateFrom) params.append(\"dateFrom\", filters.dateFrom);\n        if (filters?.dateTo) params.append(\"dateTo\", filters.dateTo);\n        if (filters?.platform) params.append(\"platform\", filters.platform);\n        if (filters?.search) params.append(\"search\", filters.search);\n        const response = await api.get(`/invoices?${params.toString()}`);\n        return response.data;\n    },\n    getInvoice: async (id)=>{\n        const response = await api.get(`/invoices/${id}`);\n        return response.data.invoice;\n    },\n    createInvoice: async (data)=>{\n        const response = await api.post(\"/invoices\", data);\n        return response.data.invoice;\n    },\n    updateInvoice: async (id, data)=>{\n        const response = await api.put(`/invoices/${id}`, data);\n        return response.data.invoice;\n    },\n    deleteInvoice: async (id)=>{\n        await api.delete(`/invoices/${id}`);\n    },\n    downloadPdf: async (id)=>{\n        const response = await api.get(`/invoices/${id}/pdf`, {\n            responseType: \"blob\"\n        });\n        return response.data;\n    },\n    recordPayment: async (id, payment)=>{\n        const response = await api.post(`/invoices/${id}/payment`, payment);\n        return response.data.invoice;\n    }\n};\n// Export the main API client for custom requests\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"05dce48e3234\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdmVyaWFwaS1kYXNoYm9hcmQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzlhMGMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwNWRjZTQ4ZTMyMzRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuth */ \"(rsc)/./src/hooks/useAuth.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\n\nconst metadata = {\n    title: \"VeriAPI Dashboard - Electronic Invoicing Management\",\n    description: \"Manage your electronic invoices, customers, and integrations with VeriAPI\",\n    keywords: [\n        \"invoicing\",\n        \"electronic invoicing\",\n        \"SaaS\",\n        \"Spanish invoicing\",\n        \"facturaci\\xf3n electr\\xf3nica\"\n    ],\n    authors: [\n        {\n            name: \"VeriAPI Team\"\n        }\n    ],\n    robots: \"index, follow\",\n    openGraph: {\n        title: \"VeriAPI Dashboard\",\n        description: \"Electronic Invoicing Management System\",\n        type: \"website\",\n        locale: \"es_ES\",\n        alternateLocale: \"en_US\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"es\",\n        className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            style: {\n                                background: \"#363636\",\n                                color: \"#fff\"\n                            },\n                            success: {\n                                duration: 3000,\n                                iconTheme: {\n                                    primary: \"#10b981\",\n                                    secondary: \"#fff\"\n                                }\n                            },\n                            error: {\n                                duration: 5000,\n                                iconTheme: {\n                                    primary: \"#ef4444\",\n                                    secondary: \"#fff\"\n                                }\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/layout.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/layout.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/layout.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/layout.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/hooks/useAuth.tsx":
/*!*******************************!*\
  !*** ./src/hooks/useAuth.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1),
/* harmony export */   useRequireAuth: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/hooks/useAuth.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/hooks/useAuth.tsx#useAuth`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/hooks/useAuth.tsx#useRequireAuth`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/react-hot-toast","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/js-cookie","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/lucide-react","vendor-chunks/zod","vendor-chunks/@hookform","vendor-chunks/react-hook-form"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();