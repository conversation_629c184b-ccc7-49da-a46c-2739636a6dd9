"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_messages_es_json";
exports.ids = ["_rsc_src_messages_es_json"];
exports.modules = {

/***/ "(rsc)/./src/messages/es.json":
/*!******************************!*\
  !*** ./src/messages/es.json ***!
  \******************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"common":{"loading":"Cargando...","error":"Error","success":"Éxito","save":"Guardar","cancel":"Cancelar","delete":"Eliminar","edit":"Editar","view":"Ver","create":"Crear","search":"Buscar","filter":"Filtrar","export":"Exportar","import":"Importar","download":"Descargar","upload":"Subir","yes":"Sí","no":"No","close":"Cerrar","back":"Volver","next":"Siguiente","previous":"Anterior","refresh":"Actualizar","total":"Total","subtotal":"Subtotal","tax":"IVA","discount":"Descuento","amount":"Importe","date":"Fecha","status":"Estado","actions":"Acciones","name":"Nombre","email":"Email","phone":"Teléfono","address":"Dirección","city":"Ciudad","postalCode":"Código Postal","country":"País","notes":"Notas"},"auth":{"login":"Iniciar Sesión","logout":"Cerrar Sesión","register":"Registrarse","email":"Email","password":"Contraseña","forgotPassword":"¿Olvidaste tu contraseña?","rememberMe":"Recordarme","loginSuccess":"Sesión iniciada correctamente","loginError":"Error al iniciar sesión","logoutSuccess":"Sesión cerrada correctamente","invalidCredentials":"Credenciales inválidas","welcomeBack":"¡Bienvenido de vuelta!","signInToAccount":"Inicia sesión en tu cuenta"},"navigation":{"dashboard":"Panel","invoices":"Facturas","customers":"Clientes","integrations":"Integraciones","subscription":"Suscripción","settings":"Configuración","profile":"Perfil","company":"Empresa"},"dashboard":{"title":"Panel de Control","welcome":"Bienvenido","overview":"Resumen","thisMonth":"Este Mes","thisYear":"Este Año","totalInvoices":"Total Facturas","totalRevenue":"Ingresos Totales","pendingAmount":"Importe Pendiente","paidAmount":"Importe Pagado","totalCustomers":"Total Clientes","activeCustomers":"Clientes Activos","recentInvoices":"Facturas Recientes","recentCustomers":"Clientes Recientes","monthlyRevenue":"Ingresos Mensuales","invoicesByStatus":"Facturas por Estado","topCustomers":"Mejores Clientes"},"invoices":{"title":"Facturas","createInvoice":"Crear Factura","invoiceNumber":"Número de Factura","customer":"Cliente","issueDate":"Fecha de Emisión","dueDate":"Fecha de Vencimiento","serviceDate":"Fecha de Servicio","total":"Total","status":"Estado","actions":"Acciones","viewInvoice":"Ver Factura","downloadPdf":"Descargar PDF","sendInvoice":"Enviar Factura","recordPayment":"Registrar Pago","markAsPaid":"Marcar como Pagada","cancelInvoice":"Cancelar Factura","duplicateInvoice":"Duplicar Factura","invoiceDetails":"Detalles de la Factura","lineItems":"Líneas de Factura","description":"Descripción","quantity":"Cantidad","unitPrice":"Precio Unitario","taxRate":"Tipo de IVA","lineTotal":"Total Línea","addLine":"Añadir Línea","removeLine":"Eliminar Línea","paymentInfo":"Información de Pago","paymentStatus":"Estado de Pago","paidAmount":"Importe Pagado","remainingAmount":"Importe Restante","paymentHistory":"Historial de Pagos","paymentDate":"Fecha de Pago","paymentMethod":"Método de Pago","paymentReference":"Referencia de Pago","statuses":{"draft":"Borrador","sent":"Enviada","viewed":"Vista","paid":"Pagada","overdue":"Vencida","cancelled":"Cancelada"},"paymentStatuses":{"pending":"Pendiente","partial":"Parcial","paid":"Pagada","overdue":"Vencida"},"filters":{"all":"Todas","dateRange":"Rango de Fechas","platform":"Plataforma","customer":"Cliente"}},"customers":{"title":"Clientes","createCustomer":"Crear Cliente","customerName":"Nombre del Cliente","customerType":"Tipo de Cliente","taxId":"Identificador Fiscal","contact":"Contacto","address":"Dirección","paymentTerms":"Términos de Pago","creditLimit":"Límite de Crédito","customerSince":"Cliente Desde","totalInvoices":"Total Facturas","totalAmount":"Importe Total","lastInvoice":"Última Factura","averagePaymentDays":"Días Promedio de Pago","customerDetails":"Detalles del Cliente","editCustomer":"Editar Cliente","deleteCustomer":"Eliminar Cliente","customerInvoices":"Facturas del Cliente","types":{"individual":"Particular","company":"Empresa"},"fields":{"name":"Nombre","tradeName":"Nombre Comercial","nif":"NIF","cif":"CIF","nie":"NIE","vatNumber":"Número de IVA","street":"Calle","number":"Número","floor":"Piso","door":"Puerta","city":"Ciudad","province":"Provincia","postalCode":"Código Postal","country":"País","phone":"Teléfono","mobile":"Móvil","email":"Email","website":"Sitio Web","paymentTerms":"Términos de Pago","preferredPaymentMethod":"Método de Pago Preferido","bankName":"Nombre del Banco","iban":"IBAN","creditLimit":"Límite de Crédito","notes":"Notas","tags":"Etiquetas"}},"integrations":{"title":"Integraciones","description":"Conecta VeriAPI con tus plataformas favoritas","connected":"Conectado","notConnected":"No Conectado","connect":"Conectar","disconnect":"Desconectar","configure":"Configurar","status":"Estado","lastSync":"Última Sincronización","totalOrders":"Total Pedidos","platforms":{"woocommerce":"WooCommerce","shopify":"Shopify","prestashop":"PrestaShop","stripe":"Stripe","paypal":"PayPal"},"woocommerce":{"title":"WooCommerce","description":"Sincroniza automáticamente los pedidos de tu tienda WooCommerce","apiKey":"Clave API","apiSecret":"Secreto API","storeUrl":"URL de la Tienda","webhookUrl":"URL del Webhook"}},"subscription":{"title":"Suscripción","currentPlan":"Plan Actual","planDetails":"Detalles del Plan","usage":"Uso","billing":"Facturación","changePlan":"Cambiar Plan","cancelSubscription":"Cancelar Suscripción","renewSubscription":"Renovar Suscripción","invoicesThisMonth":"Facturas Este Mes","invoicesLimit":"Límite de Facturas","remainingInvoices":"Facturas Restantes","nextBilling":"Próxima Facturación","plans":{"free":"Gratuito","basic":"Básico","premium":"Premium","enterprise":"Empresarial"},"features":{"invoicesPerMonth":"Facturas por mes","customers":"Clientes","integrations":"Integraciones","support":"Soporte","customization":"Personalización","apiAccess":"Acceso API","webhooks":"Webhooks","multiUser":"Multi-usuario"}},"company":{"title":"Empresa","companyInfo":"Información de la Empresa","taxSettings":"Configuración Fiscal","invoiceSettings":"Configuración de Facturación","bankInfo":"Información Bancaria","logo":"Logo","fields":{"name":"Nombre de la Empresa","tradeName":"Nombre Comercial","nif":"NIF","cif":"CIF","nie":"NIE","vatNumber":"Número de IVA","businessType":"Tipo de Negocio","sector":"Sector","defaultTaxRate":"Tipo de IVA por Defecto","irpfRate":"Tipo de IRPF","invoicePrefix":"Prefijo de Factura","nextNumber":"Próximo Número","numberLength":"Longitud del Número","dueDays":"Días de Vencimiento","bankName":"Nombre del Banco","iban":"IBAN","swift":"Código SWIFT"}},"errors":{"generic":"Ha ocurrido un error inesperado","network":"Error de conexión","unauthorized":"No autorizado","forbidden":"Acceso denegado","notFound":"No encontrado","validation":"Error de validación","server":"Error del servidor","timeout":"Tiempo de espera agotado"},"validation":{"required":"Este campo es obligatorio","email":"Debe ser un email válido","minLength":"Debe tener al menos {min} caracteres","maxLength":"No puede exceder {max} caracteres","min":"Debe ser mayor o igual a {min}","max":"Debe ser menor o igual a {max}","pattern":"Formato inválido","nif":"NIF inválido","cif":"CIF inválido","nie":"NIE inválido","postalCode":"Código postal inválido","phone":"Número de teléfono inválido","iban":"IBAN inválido"}}');

/***/ })

};
;