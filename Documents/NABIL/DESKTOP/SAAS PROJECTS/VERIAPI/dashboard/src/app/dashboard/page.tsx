'use client';

import { useTranslations } from 'next-intl';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { 
  FileText, 
  Users, 
  Euro, 
  TrendingUp, 
  Clock, 
  CheckCircle,
  AlertCircle,
  Calendar
} from 'lucide-react';
import { formatCurrency, formatNumber } from '@/lib/utils';

// Mock data for demonstration
const mockStats = {
  invoices: {
    totalInvoices: 156,
    totalAmount: 45230.50,
    paidAmount: 38450.25,
    pendingAmount: 6780.25,
    thisMonthInvoices: 23,
    thisMonthAmount: 8950.75,
  },
  customers: {
    totalCustomers: 42,
    activeCustomers: 38,
  },
  company: {
    subscription: {
      plan: 'premium' as const,
      status: 'active' as const,
      maxInvoicesPerMonth: 100,
    },
    maxInvoicesPerMonth: 100,
    remainingInvoices: 77,
  },
};

const mockRecentInvoices = [
  {
    id: '1',
    invoiceNumber: 'FAC-000156',
    customer: { name: 'Empresa ABC S.L.' },
    total: 1250.50,
    status: 'paid' as const,
    issueDate: '2024-01-15',
  },
  {
    id: '2',
    invoiceNumber: 'FAC-000155',
    customer: { name: 'Juan García' },
    total: 850.00,
    status: 'sent' as const,
    issueDate: '2024-01-14',
  },
  {
    id: '3',
    invoiceNumber: 'FAC-000154',
    customer: { name: 'Tienda Online S.L.' },
    total: 2100.75,
    status: 'overdue' as const,
    issueDate: '2024-01-10',
  },
];

export default function DashboardPage() {
  const t = useTranslations();

  const statCards = [
    {
      title: t('dashboard.totalInvoices'),
      value: formatNumber(mockStats.invoices.totalInvoices),
      change: '+12%',
      changeType: 'positive' as const,
      icon: FileText,
      color: 'blue',
    },
    {
      title: t('dashboard.totalRevenue'),
      value: formatCurrency(mockStats.invoices.totalAmount),
      change: '+8.2%',
      changeType: 'positive' as const,
      icon: Euro,
      color: 'green',
    },
    {
      title: t('dashboard.pendingAmount'),
      value: formatCurrency(mockStats.invoices.pendingAmount),
      change: '-15%',
      changeType: 'negative' as const,
      icon: Clock,
      color: 'orange',
    },
    {
      title: t('dashboard.totalCustomers'),
      value: formatNumber(mockStats.customers.totalCustomers),
      change: '+3',
      changeType: 'positive' as const,
      icon: Users,
      color: 'purple',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'sent':
        return 'bg-blue-100 text-blue-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="h-4 w-4" />;
      case 'sent':
        return <FileText className="h-4 w-4" />;
      case 'overdue':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  return (
    <DashboardLayout title={t('dashboard.title')}>
      <div className="space-y-6">
        {/* Welcome section */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg p-6 text-white">
          <h2 className="text-2xl font-bold mb-2">
            {t('dashboard.welcome')}, Usuario! 👋
          </h2>
          <p className="text-blue-100">
            Aquí tienes un resumen de tu actividad de facturación
          </p>
        </div>

        {/* Stats grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {statCards.map((stat, index) => (
            <Card key={index} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">
                      {stat.title}
                    </p>
                    <p className="text-2xl font-bold text-gray-900">
                      {stat.value}
                    </p>
                    <div className="flex items-center mt-2">
                      <TrendingUp className={`h-4 w-4 mr-1 ${
                        stat.changeType === 'positive' ? 'text-green-500' : 'text-red-500'
                      }`} />
                      <span className={`text-sm font-medium ${
                        stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {stat.change}
                      </span>
                      <span className="text-sm text-gray-500 ml-1">vs mes anterior</span>
                    </div>
                  </div>
                  <div className={`p-3 rounded-full bg-${stat.color}-100`}>
                    <stat.icon className={`h-6 w-6 text-${stat.color}-600`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Recent activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent invoices */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="h-5 w-5 mr-2" />
                {t('dashboard.recentInvoices')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockRecentInvoices.map((invoice) => (
                  <div key={invoice.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-full ${getStatusColor(invoice.status)}`}>
                        {getStatusIcon(invoice.status)}
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{invoice.invoiceNumber}</p>
                        <p className="text-sm text-gray-500">{invoice.customer.name}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-gray-900">
                        {formatCurrency(invoice.total)}
                      </p>
                      <p className="text-sm text-gray-500 flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        {invoice.issueDate}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-4 pt-4 border-t border-gray-200">
                <button className="text-sm text-blue-600 hover:text-blue-500 font-medium">
                  Ver todas las facturas →
                </button>
              </div>
            </CardContent>
          </Card>

          {/* Monthly overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="h-5 w-5 mr-2" />
                {t('dashboard.thisMonth')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                  <div>
                    <p className="text-sm text-gray-600">Facturas creadas</p>
                    <p className="text-2xl font-bold text-blue-600">
                      {mockStats.invoices.thisMonthInvoices}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-600">Ingresos</p>
                    <p className="text-lg font-semibold text-gray-900">
                      {formatCurrency(mockStats.invoices.thisMonthAmount)}
                    </p>
                  </div>
                </div>

                <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                  <div>
                    <p className="text-sm text-gray-600">Facturas restantes</p>
                    <p className="text-2xl font-bold text-green-600">
                      {mockStats.company.remainingInvoices}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-600">de {mockStats.company.maxInvoicesPerMonth}</p>
                    <div className="w-20 bg-gray-200 rounded-full h-2 mt-1">
                      <div 
                        className="bg-green-600 h-2 rounded-full" 
                        style={{ 
                          width: `${((mockStats.company.maxInvoicesPerMonth - mockStats.company.remainingInvoices) / mockStats.company.maxInvoicesPerMonth) * 100}%` 
                        }}
                      />
                    </div>
                  </div>
                </div>

                <div className="pt-4 border-t border-gray-200">
                  <button className="text-sm text-blue-600 hover:text-blue-500 font-medium">
                    Ver estadísticas completas →
                  </button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
