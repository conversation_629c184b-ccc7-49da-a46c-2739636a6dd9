{"name": "veriapi-dashboard", "version": "1.0.0", "description": "VeriAPI SaaS Dashboard - Electronic Invoicing Management", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"next": "^14.2.0", "react": "^18.3.0", "react-dom": "^18.3.0", "@tanstack/react-table": "^8.17.0", "@tanstack/react-query": "^5.40.0", "recharts": "^2.12.0", "react-hook-form": "^7.51.0", "@hookform/resolvers": "^3.3.0", "zod": "^3.23.0", "axios": "^1.7.0", "date-fns": "^3.6.0", "lucide-react": "^0.379.0", "clsx": "^2.1.0", "tailwind-merge": "^2.3.0", "react-hot-toast": "^2.4.0", "next-intl": "^3.15.0", "js-cookie": "^3.0.5", "react-pdf": "^7.7.0"}, "devDependencies": {"@types/node": "^20.14.0", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "@types/js-cookie": "^3.0.6", "typescript": "^5.4.0", "tailwindcss": "^3.4.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "eslint": "^8.57.0", "eslint-config-next": "^14.2.0", "@typescript-eslint/eslint-plugin": "^7.13.0", "@typescript-eslint/parser": "^7.13.0", "prettier": "^3.3.0", "prettier-plugin-tailwindcss": "^0.6.0"}, "engines": {"node": ">=18.0.0"}}